const { sequelize } = require('../models');

async function syncDatabase() {
  try {
    console.log('🔄 开始同步数据库...');
    
    // 同步所有模型到数据库
    await sequelize.sync({ force: false, alter: true });
    
    console.log('✅ 数据库同步完成');
    
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接测试成功');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 数据库同步失败:', error);
    process.exit(1);
  }
}

syncDatabase();
