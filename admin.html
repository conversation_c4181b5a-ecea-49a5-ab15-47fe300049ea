<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音电商后台管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }

        .tab.active {
            background: #1890ff;
            color: white;
        }

        .tab:hover {
            background: #40a9ff;
            color: white;
        }

        .content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .btn-danger {
            background: #ff4d4f;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .avatar-upload {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .avatar-preview {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }

        .avatar-preview img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .file-input {
            display: none;
        }

        .file-label {
            background: #f0f0f0;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .file-label:hover {
            background: #e0e0e0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 500;
        }

        .table tr:hover {
            background: #f9f9f9;
        }

        .status-active {
            color: #52c41a;
        }

        .status-inactive {
            color: #faad14;
        }

        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .message.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .message.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .hidden {
            display: none;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 抖音电商后台管理</h1>
            <p>管理您的店铺信息、商品和数据</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('shop')">店铺管理</button>
            <button class="tab" onclick="showTab('products')">商品管理</button>
            <button class="tab" onclick="showTab('data')">数据管理</button>
        </div>

        <!-- 店铺管理 -->
        <div id="shop-content" class="content">
            <h2>店铺信息设置</h2>
            <div id="message"></div>
            
            <form id="shop-form">
                <div class="grid">
                    <div>
                        <div class="form-group">
                            <label for="shop_name">店铺名称</label>
                            <input type="text" id="shop_name" name="shop_name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="shop_score">商家体验分</label>
                            <input type="number" id="shop_score" name="shop_score" min="0" max="100" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">店铺描述</label>
                            <textarea id="description" name="description" placeholder="请输入店铺描述"></textarea>
                        </div>
                    </div>
                    
                    <div>
                        <div class="form-group">
                            <label>店铺头像</label>
                            <div class="avatar-upload">
                                <div class="avatar-preview" id="avatar-preview">W</div>
                                <div>
                                    <input type="file" id="avatar-file" class="file-input" accept="image/*">
                                    <label for="avatar-file" class="file-label">选择头像</label>
                                    <p style="margin-top: 8px; color: #666; font-size: 12px;">支持 JPG、PNG 格式，建议尺寸 200x200</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn">保存设置</button>
            </form>
        </div>

        <!-- 商品管理 -->
        <div id="products-content" class="content hidden">
            <h2>商品管理</h2>
            <div id="products-message"></div>
            
            <button class="btn" onclick="showAddProductForm()">添加新商品</button>
            
            <div id="add-product-form" class="hidden" style="margin-top: 20px; padding: 20px; background: #f9f9f9; border-radius: 6px;">
                <h3>添加商品</h3>
                <form id="product-form">
                    <div class="grid">
                        <div>
                            <div class="form-group">
                                <label for="product_name">商品名称</label>
                                <input type="text" id="product_name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="product_price">价格</label>
                                <input type="number" id="product_price" name="price" step="0.01" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="product_stock">库存</label>
                                <input type="number" id="product_stock" name="stock" required>
                            </div>
                        </div>
                        
                        <div>
                            <div class="form-group">
                                <label for="product_category">分类</label>
                                <select id="product_category" name="category_id" required>
                                    <option value="">请选择分类</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="product_description">商品描述</label>
                                <textarea id="product_description" name="description"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">添加商品</button>
                    <button type="button" class="btn" onclick="hideAddProductForm()" style="background: #666; margin-left: 10px;">取消</button>
                </form>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>商品名称</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>销量</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="products-table">
                    <!-- 商品列表将在这里动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 数据管理 -->
        <div id="data-content" class="content hidden">
            <h2>数据管理</h2>
            <p>这里可以管理各种业务数据，如销售数据、流量数据等。</p>
            <p style="color: #666; margin-top: 10px;">功能开发中...</p>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
