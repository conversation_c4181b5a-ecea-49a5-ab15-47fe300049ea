const { sequelize } = require('../config/database');
const { DataTypes } = require('sequelize');

// 店铺信息模型
const ShopInfo = sequelize.define('ShopInfo', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  shop_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '店铺名称'
  },
  shop_avatar: {
    type: DataTypes.STRING(255),
    defaultValue: 'W',
    comment: '店铺头像'
  },
  shop_score: {
    type: DataTypes.INTEGER,
    defaultValue: 70,
    comment: '商家体验分'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '店铺描述'
  },
  background_image: {
    type: DataTypes.STRING(255),
    comment: '背景图片'
  }
}, {
  tableName: 'shop_info',
  comment: '店铺信息表'
});

// 商品分类模型
const Category = sequelize.define('Category', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '分类名称'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '分类描述'
  }
}, {
  tableName: 'categories',
  comment: '商品分类表'
});

// 商品模型
const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '商品名称'
  },
  category_id: {
    type: DataTypes.INTEGER,
    comment: '分类ID'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '价格'
  },
  stock: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '库存'
  },
  sales: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '销量'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0.00,
    comment: '评分'
  },
  views: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '浏览量'
  },
  conversion_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: '转化率'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'deleted'),
    defaultValue: 'active',
    comment: '状态'
  },
  image_url: {
    type: DataTypes.STRING(255),
    comment: '商品图片'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '商品描述'
  }
}, {
  tableName: 'products',
  comment: '商品表'
});

// 销售数据模型
const SalesData = sequelize.define('SalesData', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    unique: true,
    comment: '日期'
  },
  total_sales: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0.00,
    comment: '总销售额'
  },
  total_orders: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '总订单数'
  },
  avg_order_value: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    comment: '平均订单价值'
  },
  first_purchase_amount: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0.00,
    comment: '首购金额'
  },
  repurchase_amount: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0.00,
    comment: '复购金额'
  },
  refund_amount: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0.00,
    comment: '退款金额'
  }
}, {
  tableName: 'sales_data',
  comment: '销售数据表'
});

// 流量数据模型
const TrafficData = sequelize.define('TrafficData', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    unique: true,
    comment: '日期'
  },
  exposure: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '曝光量'
  },
  clicks: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '点击量'
  },
  customers: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '客户数'
  }
}, {
  tableName: 'traffic_data',
  comment: '流量数据表'
});

// 渠道分布模型
const ChannelDistribution = sequelize.define('ChannelDistribution', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  channel_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '渠道名称'
  },
  amount: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0.00,
    comment: '金额'
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '日期'
  }
}, {
  tableName: 'channel_distribution',
  comment: '渠道分布表'
});

// 搜索关键词模型
const SearchKeyword = sequelize.define('SearchKeyword', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  keyword: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '关键词'
  },
  search_volume: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '搜索量'
  },
  competition: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    defaultValue: 'medium',
    comment: '竞争度'
  },
  trend: {
    type: DataTypes.STRING(20),
    comment: '趋势'
  }
}, {
  tableName: 'search_keywords',
  comment: '搜索关键词表'
});

// 热门商品搜索模型
const HotProductSearch = sequelize.define('HotProductSearch', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  product_name: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '商品名称'
  },
  searches: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '搜索次数'
  },
  clicks: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '点击次数'
  },
  conversion_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: '转化率'
  }
}, {
  tableName: 'hot_product_searches',
  comment: '热门商品搜索表'
});

// 支付方式统计模型
const PaymentMethod = sequelize.define('PaymentMethod', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  method_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '支付方式'
  },
  percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: '占比'
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '日期'
  }
}, {
  tableName: 'payment_methods',
  comment: '支付方式统计表'
});

// 市场趋势模型
const MarketTrend = sequelize.define('MarketTrend', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '类别'
  },
  growth_rate: {
    type: DataTypes.STRING(20),
    comment: '增长率'
  },
  volume: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '交易量'
  },
  competition: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    defaultValue: 'medium',
    comment: '竞争度'
  }
}, {
  tableName: 'market_trends',
  comment: '市场趋势表'
});

// 竞品分析模型
const Competitor = sequelize.define('Competitor', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '竞品名称'
  },
  market_share: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: '市场份额'
  },
  growth_rate: {
    type: DataTypes.STRING(20),
    comment: '增长率'
  },
  strength: {
    type: DataTypes.STRING(200),
    comment: '优势'
  }
}, {
  tableName: 'competitors',
  comment: '竞品分析表'
});

// 客服数据模型
const CustomerService = sequelize.define('CustomerService', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    unique: true,
    comment: '日期'
  },
  total_tickets: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '总工单数'
  },
  resolved: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '已解决'
  },
  pending: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '待处理'
  },
  avg_response_time: {
    type: DataTypes.STRING(20),
    comment: '平均响应时间'
  },
  satisfaction: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0.00,
    comment: '满意度'
  }
}, {
  tableName: 'customer_service',
  comment: '客服数据表'
});

// 物流数据模型
const Logistics = sequelize.define('Logistics', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    unique: true,
    comment: '日期'
  },
  total_orders: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '总订单数'
  },
  shipped: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '已发货'
  },
  delivered: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '已送达'
  },
  avg_delivery_time: {
    type: DataTypes.STRING(20),
    comment: '平均配送时间'
  },
  return_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: '退货率'
  }
}, {
  tableName: 'logistics',
  comment: '物流数据表'
});

// 售后数据模型
const AfterSales = sequelize.define('AfterSales', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    unique: true,
    comment: '日期'
  },
  returns: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '退货数'
  },
  exchanges: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '换货数'
  },
  refunds: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '退款数'
  },
  avg_process_time: {
    type: DataTypes.STRING(20),
    comment: '平均处理时间'
  },
  satisfaction: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0.00,
    comment: '满意度'
  }
}, {
  tableName: 'after_sales',
  comment: '售后数据表'
});

// 定义关联关系
Product.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });
Category.hasMany(Product, { foreignKey: 'category_id', as: 'products' });

module.exports = {
  sequelize,
  ShopInfo,
  Category,
  Product,
  SalesData,
  TrafficData,
  ChannelDistribution,
  SearchKeyword,
  HotProductSearch,
  PaymentMethod,
  MarketTrend,
  Competitor,
  CustomerService,
  Logistics,
  AfterSales
};
