// API测试脚本
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8083';

async function testAPI() {
    console.log('🧪 开始API测试...\n');
    
    try {
        // 1. 测试获取店铺信息
        console.log('1️⃣ 测试获取店铺信息...');
        const shopResponse = await fetch(`${BASE_URL}/api/shop`);
        const shopData = await shopResponse.json();
        console.log('✅ 店铺信息:', shopData);
        console.log('');
        
        // 2. 测试更新店铺信息
        console.log('2️⃣ 测试更新店铺信息...');
        const updateData = {
            shop_name: '测试店铺名称',
            shop_score: 85,
            description: '这是一个测试描述'
        };
        
        const updateResponse = await fetch(`${BASE_URL}/api/shop`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });
        
        if (updateResponse.ok) {
            const updatedShop = await updateResponse.json();
            console.log('✅ 店铺信息更新成功:', updatedShop);
        } else {
            console.log('❌ 店铺信息更新失败');
        }
        console.log('');
        
        // 3. 测试获取商品列表
        console.log('3️⃣ 测试获取商品列表...');
        const productsResponse = await fetch(`${BASE_URL}/api/products`);
        const productsData = await productsResponse.json();
        console.log('✅ 商品列表:', productsData.length, '个商品');
        console.log('');
        
        // 4. 测试添加商品
        console.log('4️⃣ 测试添加商品...');
        const newProduct = {
            name: '测试商品',
            category_id: 1,
            price: 99.99,
            stock: 100,
            description: '这是一个测试商品'
        };
        
        const addProductResponse = await fetch(`${BASE_URL}/api/products`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newProduct)
        });
        
        if (addProductResponse.ok) {
            const addedProduct = await addProductResponse.json();
            console.log('✅ 商品添加成功:', addedProduct);
            
            // 5. 测试删除商品
            console.log('5️⃣ 测试删除商品...');
            const deleteResponse = await fetch(`${BASE_URL}/api/products/${addedProduct.id}`, {
                method: 'DELETE'
            });
            
            if (deleteResponse.ok) {
                console.log('✅ 商品删除成功');
            } else {
                console.log('❌ 商品删除失败');
            }
        } else {
            console.log('❌ 商品添加失败');
        }
        console.log('');
        
        // 6. 测试获取分类
        console.log('6️⃣ 测试获取分类...');
        const categoriesResponse = await fetch(`${BASE_URL}/api/categories`);
        const categoriesData = await categoriesResponse.json();
        console.log('✅ 分类列表:', categoriesData.length, '个分类');
        console.log('');
        
        // 7. 恢复原始店铺信息
        console.log('7️⃣ 恢复原始店铺信息...');
        const restoreData = {
            shop_name: '文恩臻选阁',
            shop_score: 70,
            description: '精选优质商品，为您提供最佳购物体验'
        };
        
        const restoreResponse = await fetch(`${BASE_URL}/api/shop`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(restoreData)
        });
        
        if (restoreResponse.ok) {
            console.log('✅ 店铺信息已恢复');
        } else {
            console.log('❌ 店铺信息恢复失败');
        }
        
        console.log('\n🎉 所有API测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

testAPI();
