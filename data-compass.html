<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据罗盘 - 抖音电商</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            writing-mode: horizontal-tb;
            text-orientation: mixed;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', sans-serif;
            background: #f5f5f5;
            color: #333;
            overflow-x: hidden;
            writing-mode: horizontal-tb;
            text-orientation: mixed;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #f5f5f5;
            min-height: 100vh;
            position: relative;
            writing-mode: horizontal-tb;
            direction: ltr;
        }

        /* Top Section with Background */
        .top-section {
            background: url('./7.png') no-repeat center center;
            background-size: 100% 100%;
            position: relative;
            width: 100vw;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }

        /* Header */
        .header {
            background: transparent;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            border-bottom: none;
        }

        .back-btn {
            font-size: 18px;
            color: #333;
            margin-right: 12px;
            cursor: pointer;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .header-subtitle {
            font-size: 13px;
            color: #999;
            margin-left: 8px;
        }

        /* Navigation Tabs */
        .nav-tabs {
            background: transparent;
            padding: 0 16px;
            display: flex;
            border-bottom: none;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            padding: 12px 16px;
            font-size: 15px;
            color: #666;
            cursor: pointer;
            position: relative;
            writing-mode: horizontal-tb;
            white-space: nowrap;
            flex-shrink: 0;
            min-width: fit-content;
        }

        .nav-tab.active {
            color: #333;
            font-weight: 600;
        }

        .market-tab {
            position: relative;
        }

        .market-tab::after {
            content: '';
            position: absolute;
            top: 8px;
            right: 8px;
            width: 6px;
            height: 6px;
            background: #ff4757;
            border-radius: 50%;
        }

        /* Time Filter */
        .time-filter {
            background: transparent;
            padding: 12px 16px;
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .time-filter::-webkit-scrollbar {
            display: none;
        }

        .time-btn {
            padding: 6px 16px;
            border: none;
            border-radius: 4px;
            background: white;
            color: #666;
            font-size: 13px;
            cursor: pointer;
            flex-shrink: 0;
            white-space: nowrap;
            min-width: fit-content;
        }

        .time-btn.active {
            background: #4285f4;
            color: white;
            border-color: #4285f4;
        }

        /* Core Data Section */
        .core-data {
            background: white;
            margin: 0 0 12px 0;
            border-radius: 0;
            padding: 16px;
            width: 100vw;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }

        .core-data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .core-data-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .core-data-date {
            font-size: 13px;
            color: #999;
        }

        .analysis-link {
            font-size: 13px;
            color: #999;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .analysis-link::after {
            content: '>';
            margin-left: 4px;
        }

        /* Data Type Tabs */
        .data-type-tabs {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
        }

        .data-type-tab {
            font-size: 14px;
            color: #666;
            cursor: pointer;
            padding-bottom: 4px;
        }

        .data-type-tab.active {
            color: #4285f4;
            border-bottom: 2px solid #4285f4;
        }

        /* Data Grid */
        .data-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
        }

        .data-item {
            text-align: left;
        }

        .data-item.highlighted {
            border: 1px solid #4285f4;
            border-radius: 8px;
            padding: 12px;
            background: #f8f9ff;
        }

        .data-label {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
        }

        .data-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .data-compare {
            font-size: 12px;
            color: #999;
            margin-bottom: 2px;
        }

        .data-baseline {
            font-size: 12px;
            color: #999;
        }

        .positive {
            color: #00c851;
        }

        .negative {
            color: #ff4444;
        }

        /* Expand Button */
        .expand-btn {
            text-align: center;
            padding: 16px;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .expand-btn::after {
            content: '⌄';
            margin-left: 4px;
        }

        /* Bottom Section */
        .bottom-section {
            background: white;
            margin: 0 16px;
            border-radius: 8px;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .bottom-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .expand-icon {
            font-size: 14px;
            color: #666;
        }

        .bottom-text {
            font-size: 14px;
            color: #666;
        }

        .all-link {
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
        }

        .all-link::after {
            content: '>';
            margin-left: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Top Section with Background -->
        <div class="top-section">
            <!-- Header -->
            <div class="header">
                <span class="back-btn" onclick="goBack()">‹</span>
                <span class="header-title">数据罗盘</span>
                <span class="header-subtitle">抖音电商数据，就用罗盘</span>
            </div>

            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <div class="nav-tab">首页</div>
                <div class="nav-tab">搜索</div>
                <div class="nav-tab active">交易</div>
                <div class="nav-tab">商品</div>
                <div class="nav-tab market-tab">市场</div>
                <div class="nav-tab">服务</div>
                <div class="nav-tab">商家</div>
            </div>

            <!-- Time Filter -->
            <div class="time-filter">
                <button class="time-btn">今日</button>
                <button class="time-btn">昨日</button>
                <button class="time-btn active">近7日</button>
                <button class="time-btn">近30日</button>
                <button class="time-btn">自然日</button>
                <button class="time-btn">自然月</button>
            </div>
        </div>

            <!-- Core Data Section -->
            <div class="core-data">
            <div class="core-data-header">
                <div>
                    <div class="core-data-title">核心数据</div>
                    <div class="core-data-date">7月26日-8月01日</div>
                </div>
                <div class="analysis-link">售后分析及优化</div>
            </div>

            <!-- Data Type Tabs -->
            <div class="data-type-tabs">
                <div class="data-type-tab active">整体</div>
                <div class="data-type-tab">自营</div>
                <div class="data-type-tab">合作</div>
            </div>

            <!-- Data Grid -->
            <div class="data-grid">
                <div class="data-item highlighted">
                    <div class="data-label">用户支付金额</div>
                    <div class="data-value">¥0</div>
                    <div class="data-compare">环比 -</div>
                    <div class="data-baseline">基准 149.4</div>
                </div>
                <div class="data-item">
                    <div class="data-label">退款金额(支付时间)</div>
                    <div class="data-value">-</div>
                    <div class="data-compare">环比 -</div>
                </div>
                <div class="data-item">
                    <div class="data-label">退款后用户支付金额</div>
                    <div class="data-value">-</div>
                    <div class="data-compare">环比 -</div>
                </div>
                <div class="data-item">
                    <div class="data-label">退款率(支付时间)</div>
                    <div class="data-value">-</div>
                    <div class="data-compare">环比 -</div>
                </div>
                <div class="data-item">
                    <div class="data-label">成交订单数</div>
                    <div class="data-value">0</div>
                    <div class="data-compare">环比 -</div>
                    <div class="data-baseline">基准 4</div>
                </div>
                <div class="data-item">
                    <div class="data-label">退款订单数(退款时间)</div>
                    <div class="data-value">0</div>
                    <div class="data-compare positive">环比 ↓ 100%</div>
                    <div class="data-baseline">标杆 0</div>
                </div>
                <div class="data-item">
                    <div class="data-label">退款金额(退款时间)</div>
                    <div class="data-value">¥0</div>
                    <div class="data-compare positive">环比 ↓ 100%</div>
                    <div class="data-baseline">标杆 0</div>
                </div>
                <div class="data-item">
                    <div class="data-label">成交人数</div>
                    <div class="data-value">0</div>
                    <div class="data-compare">环比 -</div>
                    <div class="data-baseline">基准 4</div>
                </div>
                <div class="data-item">
                    <div class="data-label">客单价</div>
                    <div class="data-value">-</div>
                    <div class="data-compare">环比 -</div>
                    <div class="data-baseline">基准 30.36</div>
                </div>
            </div>

            <!-- Expand Button -->
            <div class="expand-btn">展开趋势&载体</div>
        </div>
        </div>

        <!-- Bottom Section -->
        <div class="bottom-section">
            <div class="bottom-left">
                <span class="expand-icon">⌄</span>
                <span class="bottom-text">展开</span>
                <span class="bottom-text">幻成</span>
            </div>
            <div class="all-link">全部</div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // 数据类型切换
        document.querySelectorAll('.data-type-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.data-type-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 时间筛选切换
        document.querySelectorAll('.time-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.time-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 导航标签切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 防止导航标签滑动时页面跟着滚动
        const navTabs = document.querySelector('.nav-tabs');
        navTabs.addEventListener('touchstart', function(e) {
            e.stopPropagation();
        });

        navTabs.addEventListener('touchmove', function(e) {
            e.stopPropagation();
        });

        navTabs.addEventListener('touchend', function(e) {
            e.stopPropagation();
        });

        // 防止时间筛选滑动时页面跟着滚动
        const timeFilter = document.querySelector('.time-filter');
        timeFilter.addEventListener('touchstart', function(e) {
            e.stopPropagation();
        });

        timeFilter.addEventListener('touchmove', function(e) {
            e.stopPropagation();
        });

        timeFilter.addEventListener('touchend', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
