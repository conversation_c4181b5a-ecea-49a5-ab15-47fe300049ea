<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文恩臻选阁 - 抖音电商</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', sans-serif;
            background: linear-gradient(180deg, #e8f4fd 0%, #d6e9f7 50%, #c4ddf0 100%);
            color: #333;
            overflow-x: hidden;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            background-attachment: fixed;
        }

        .container {
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            background: url('./2.png') no-repeat center top, linear-gradient(180deg, #e8f4fd 0%, #d6e9f7 50%, #c4ddf0 100%);
            background-size: contain, cover;
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        /* Status Bar */
        .status-bar {
            display: none;
        }

        .time {
            color: #333;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #333;
        }
        
        /* AI Image at Top */
        .ai-image-top {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 100;
            cursor: move;
        }

        .ai-image-top img {
            width: auto;
            height: auto;
            max-width: none;
            transform: scale(0.8);
            transform-origin: top left;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            pointer-events: auto;
        }

        /* 6.png Image */
        .six-image {
            position: fixed;
            top: 100px;
            right: 10px;
            z-index: 100;
            cursor: move;
        }

        .six-image img {
            width: auto;
            height: auto;
            max-width: none;
            transform: scale(0.8);
            transform-origin: top left;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            pointer-events: auto;
        }

        /* Header */
        .header {
            background: transparent;
            padding: 20px 20px 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 5;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .menu-dots {
            font-size: 20px;
            color: #666;
            margin-right: 4px;
        }

        .shop-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .shop-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #4a7c59;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        .shop-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .shop-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            line-height: 1.2;
        }

        .shop-score {
            font-size: 12px;
            color: #666;
            line-height: 1.2;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }


        
        /* Top Stats Row */
        .top-stats {
            display: flex;
            justify-content: space-between;
            padding: 20px;
            margin-bottom: 20px;
        }

        .top-stat {
            text-align: center;
            flex: 1;
            cursor: pointer;
            transition: background-color 0.2s;
            border-radius: 8px;
            padding: 8px 4px;
        }

        .top-stat:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .top-stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .top-stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        /* Bottom Stats Row */
        .bottom-stats {
            display: flex;
            justify-content: space-between;
            padding: 20px;
            margin-bottom: 30px;
            background: white;
            border-radius: 12px;
            margin: 0 20px 30px;
        }

        .bottom-stat {
            text-align: center;
            flex: 1;
        }

        .bottom-stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .bottom-stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        /* Icon Grid */
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 16px;
            padding: 0 20px 20px;
            margin-bottom: 20px;
        }

        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #333;
        }

        .icon-wrapper {
            position: relative;
            margin-bottom: 8px;
        }

        .icon-bg {
            width: 52px;
            height: 52px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 精确匹配截图中的图标颜色 */
        .icon-bg.blue { background: #4A90E2; }      /* 商品管理 - 蓝色 */
        .icon-bg.green { background: #7ED321; }     /* 订单管理 - 绿色 */
        .icon-bg.orange { background: #F5A623; }    /* 售后管理 - 橙色 */
        .icon-bg.teal { background: #50E3C2; }      /* 评价管理 - 青色 */
        .icon-bg.yellow { background: #F8E71C; }    /* 账户提现 - 黄色 */
        .icon-bg.purple { background: #9013FE; }    /* 数据 - 紫色 */
        .icon-bg.pink { background: #E91E63; }      /* 流量推广 - 粉色 */
        .icon-bg.indigo { background: #BD10E0; }    /* 达人合作 - 靛蓝 */
        .icon-bg.cyan { background: #00BCD4; }      /* 商城运营 - 青蓝 */
        .icon-bg.red { background: #F44336; }       /* 优惠券 - 红色 */

        .icon-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            background: #FF4444;
            color: white;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 10px;
            font-weight: bold;
            border: 2px solid white;
        }

        .icon-label {
            font-size: 13px;
            color: #333;
            text-align: center;
            line-height: 1.2;
            font-weight: 500;
        }

        /* Image Section */
        .image-section {
            margin: 0;
            padding: 0;
            background: white;
            width: 100vw;
            height: 100vh;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }

        .image-section img {
            width: 100vw;
            height: 100vh;
            object-fit: cover;
            display: block;
            border: none;
            margin: 0;
            padding: 0;
        }
        
        /* Store Info Section */
        .store-info {
            background: rgba(255, 255, 255, 0.8);
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .store-tag {
            background: #4285f4;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .store-text {
            font-size: 14px;
            color: #333;
            margin-left: 8px;
            flex: 1;
        }

        .store-arrow {
            color: #666;
            font-size: 16px;
        }

        /* Content Section */
        .content-section {
            background: rgba(255, 255, 255, 0.8);
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 16px;
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .content-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .content-highlight {
            font-size: 14px;
            color: #333;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .content-stats {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 14px;
        }

        .stat-highlight {
            color: #ff6b35;
            font-weight: 600;
        }

        .action-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }
        
        /* Info Section */
        .info-section {
            background: #fff;
            padding: 16px 20px;
            margin-bottom: 12px;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .info-content {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        /* Growth Section */
        .growth-section {
            background: #fff;
            padding: 16px 20px;
            margin-bottom: 12px;
        }
        
        .growth-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .growth-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .growth-more {
            font-size: 14px;
            color: #4285f4;
        }
        
        .growth-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .growth-item:last-child {
            border-bottom: none;
        }
        
        .growth-left {
            display: flex;
            align-items: center;
        }
        
        .growth-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #e3f2fd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #2196f3;
        }
        
        .growth-text {
            font-size: 14px;
            color: #333;
        }
        
        .growth-action {
            background: #4285f4;
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            border: none;
            cursor: pointer;
        }

        /* Bottom Navigation Image */
        .bottom-nav-image {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            z-index: 10;
        }

        /* 响应式设计 - 确保背景图片在所有屏幕尺寸下正确显示 */
        @media screen and (max-width: 768px) {
            .container {
                background-size: contain, cover;
                background-position: center top, center center;
            }
        }

        @media screen and (min-width: 769px) {
            .container {
                background-size: contain, cover;
                background-position: center top, center center;
            }
        }

        /* 开发者工具打开时的适配 */
        @media screen and (max-height: 600px) {
            .container {
                background-size: contain, cover;
                background-position: center top, center center;
                min-height: 100vh;
                height: auto;
            }
        }

        /* 确保背景图片始终覆盖整个视窗 */
        @media screen {
            .container {
                background-repeat: no-repeat, no-repeat;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="time">2:38</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- AI Image at Top -->
        <div class="ai-image-top">
            <img src="./ai.png" alt="AI图片" style="width: auto; height: auto; display: block; cursor: move;" draggable="true">
        </div>

        <!-- 6.png Image -->
        <div class="six-image">
            <img src="./6.png" alt="6图片" style="width: auto; height: auto; display: block; cursor: move;" draggable="true">
        </div>

        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <div class="menu-dots">⋮</div>
                <div class="shop-info">
                    <div class="shop-avatar">W</div>
                    <div class="shop-details">
                        <div class="shop-name">文恩臻选阁</div>
                        <div class="shop-score">商家体验分 70分</div>
                    </div>
                </div>
            </div>
            <div class="header-right">
            </div>
        </div>
        
        <!-- Top Stats -->
        <div class="top-stats">
            <div class="top-stat" onclick="openDataCompass()">
                <div class="top-stat-label">用户支付金额</div>
                <div class="top-stat-value">0</div>
            </div>
            <div class="top-stat" onclick="openDataCompass()">
                <div class="top-stat-label">订单数</div>
                <div class="top-stat-value">0</div>
            </div>
            <div class="top-stat" onclick="openDataCompass()">
                <div class="top-stat-label">商品曝光</div>
                <div class="top-stat-value">0</div>
            </div>
            <div class="top-stat" onclick="openDataCompass()">
                <div class="top-stat-label">商品点击</div>
                <div class="top-stat-value">0</div>
            </div>
        </div>

        <!-- Bottom Stats -->
        <div class="bottom-stats">
            <div class="bottom-stat">
                <div class="bottom-stat-label">待支付</div>
                <div class="bottom-stat-value">0</div>
            </div>
            <div class="bottom-stat">
                <div class="bottom-stat-label">待发货</div>
                <div class="bottom-stat-value">0</div>
            </div>
            <div class="bottom-stat">
                <div class="bottom-stat-label">异常包裹</div>
                <div class="bottom-stat-value">0</div>
            </div>
            <div class="bottom-stat">
                <div class="bottom-stat-label">待售后</div>
                <div class="bottom-stat-value">0</div>
            </div>
            <div class="bottom-stat">
                <div class="bottom-stat-label">服务工单</div>
                <div class="bottom-stat-value">0</div>
            </div>
        </div>
        
        <!-- 显示PNG图片 -->
        <div class="image-section">
            <img src="./1675ca3a609b449cadfb71e8b295689f.png" alt="抖音电商界面" style="width: 100%; height: auto; display: block;">
        </div>

        <!-- Bottom Navigation with 33.png -->
        <div class="bottom-nav-image">
            <img src="./33.png" alt="底部导航" style="width: 100%; height: auto; display: block;">
        </div>




    </div>

    <script>
        function showDataPage() {
            window.location.href = '/data-dashboard.html';
        }

        function openDataCompass() {
            window.location.href = '/data-compass.html';
        }

        // 模拟数据加载
        function loadData() {
            // 这里可以添加API调用来获取真实数据
            console.log('页面数据已加载');
        }

        // 拖拽功能
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;
        let activeElement = null;

        const aiImage = document.querySelector('.ai-image-top');
        const sixImage = document.querySelector('.six-image');

        // 6.png的拖拽变量
        let isDragging6 = false;
        let currentX6;
        let currentY6;
        let initialX6;
        let initialY6;
        let xOffset6 = 0;
        let yOffset6 = 0;

        aiImage.addEventListener('mousedown', dragStart);
        sixImage.addEventListener('mousedown', dragStart6);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        // 触摸事件支持
        aiImage.addEventListener('touchstart', dragStart);
        sixImage.addEventListener('touchstart', dragStart6);
        document.addEventListener('touchmove', drag);
        document.addEventListener('touchend', dragEnd);

        function dragStart(e) {
            e.preventDefault();
            e.stopPropagation();

            const clientX = e.clientX || e.touches[0].clientX;
            const clientY = e.clientY || e.touches[0].clientY;

            initialX = clientX - xOffset;
            initialY = clientY - yOffset;

            if (e.target === aiImage || aiImage.contains(e.target)) {
                isDragging = true;
                document.body.style.overflow = 'hidden'; // 禁止页面滚动
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                e.stopPropagation();

                const clientX = e.clientX || e.touches[0].clientX;
                const clientY = e.clientY || e.touches[0].clientY;

                currentX = clientX - initialX;
                currentY = clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                aiImage.style.transform = `translate(${currentX}px, ${currentY}px) scale(0.8)`;
            }

            if (isDragging6) {
                e.preventDefault();
                e.stopPropagation();

                const clientX = e.clientX || e.touches[0].clientX;
                const clientY = e.clientY || e.touches[0].clientY;

                currentX6 = clientX - initialX6;
                currentY6 = clientY - initialY6;

                xOffset6 = currentX6;
                yOffset6 = currentY6;

                sixImage.style.transform = `translate(${currentX6}px, ${currentY6}px) scale(0.8)`;
            }
        }

        function dragEnd(e) {
            if (isDragging) {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
            }
            if (isDragging6) {
                initialX6 = currentX6;
                initialY6 = currentY6;
                isDragging6 = false;
            }
            document.body.style.overflow = ''; // 恢复页面滚动
        }

        // 6.png拖拽函数
        function dragStart6(e) {
            e.preventDefault();
            e.stopPropagation();

            const clientX = e.clientX || e.touches[0].clientX;
            const clientY = e.clientY || e.touches[0].clientY;

            initialX6 = clientX - xOffset6;
            initialY6 = clientY - yOffset6;

            if (e.target === sixImage || sixImage.contains(e.target)) {
                isDragging6 = true;
                document.body.style.overflow = 'hidden'; // 禁止页面滚动
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
