<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .result {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success {
            color: #52c41a;
        }
        .error {
            color: #ff4d4f;
        }
        .loading {
            color: #1890ff;
        }
    </style>
</head>
<body>
    <h1>🧪 前端数据加载测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">1. 店铺信息测试</h2>
        <div id="shop-test" class="result loading">正在加载店铺信息...</div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">2. 商品数据测试</h2>
        <div id="products-test" class="result loading">正在加载商品数据...</div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">3. 概览数据测试</h2>
        <div id="overview-test" class="result loading">正在加载概览数据...</div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">4. 分类数据测试</h2>
        <div id="categories-test" class="result loading">正在加载分类数据...</div>
    </div>

    <script>
        // 测试店铺信息
        async function testShopInfo() {
            try {
                const response = await fetch('/api/shop');
                const data = await response.json();
                
                document.getElementById('shop-test').innerHTML = `
                    <div class="success">✅ 店铺信息加载成功</div>
                    <div>店铺名称: ${data.shop_name}</div>
                    <div>商家体验分: ${data.shop_score}分</div>
                    <div>店铺描述: ${data.description}</div>
                    <div>头像: ${data.shop_avatar}</div>
                `;
                document.getElementById('shop-test').className = 'result success';
            } catch (error) {
                document.getElementById('shop-test').innerHTML = `
                    <div class="error">❌ 店铺信息加载失败: ${error.message}</div>
                `;
                document.getElementById('shop-test').className = 'result error';
            }
        }

        // 测试商品数据
        async function testProducts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                
                document.getElementById('products-test').innerHTML = `
                    <div class="success">✅ 商品数据加载成功</div>
                    <div>商品总数: ${data.length}个</div>
                    <div>商品列表:</div>
                    ${data.map(product => `
                        <div style="margin-left: 20px;">
                            • ${product.name} - ¥${product.price} (库存: ${product.stock}, 销量: ${product.sales})
                        </div>
                    `).join('')}
                `;
                document.getElementById('products-test').className = 'result success';
            } catch (error) {
                document.getElementById('products-test').innerHTML = `
                    <div class="error">❌ 商品数据加载失败: ${error.message}</div>
                `;
                document.getElementById('products-test').className = 'result error';
            }
        }

        // 测试概览数据
        async function testOverview() {
            try {
                const response = await fetch('/api/overview');
                const data = await response.json();
                
                document.getElementById('overview-test').innerHTML = `
                    <div class="success">✅ 概览数据加载成功</div>
                    <div>总销售额: ¥${data.paymentAmount.total}</div>
                    <div>总订单数: ${data.orders.total}</div>
                    <div>数据结构完整: ${Object.keys(data).length}个主要字段</div>
                `;
                document.getElementById('overview-test').className = 'result success';
            } catch (error) {
                document.getElementById('overview-test').innerHTML = `
                    <div class="error">❌ 概览数据加载失败: ${error.message}</div>
                `;
                document.getElementById('overview-test').className = 'result error';
            }
        }

        // 测试分类数据
        async function testCategories() {
            try {
                const response = await fetch('/api/categories');
                const data = await response.json();
                
                document.getElementById('categories-test').innerHTML = `
                    <div class="success">✅ 分类数据加载成功</div>
                    <div>分类总数: ${data.length}个</div>
                    <div>分类列表:</div>
                    ${data.map(category => `
                        <div style="margin-left: 20px;">
                            • ${category.name} - ${category.description}
                        </div>
                    `).join('')}
                `;
                document.getElementById('categories-test').className = 'result success';
            } catch (error) {
                document.getElementById('categories-test').innerHTML = `
                    <div class="error">❌ 分类数据加载失败: ${error.message}</div>
                `;
                document.getElementById('categories-test').className = 'result error';
            }
        }

        // 页面加载完成后执行所有测试
        document.addEventListener('DOMContentLoaded', function() {
            testShopInfo();
            testProducts();
            testOverview();
            testCategories();
        });
    </script>
</body>
</html>
