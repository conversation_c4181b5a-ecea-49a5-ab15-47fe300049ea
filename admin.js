// 后台管理JavaScript

// 全局变量
let currentTab = 'shop';
let shopData = {};
let productsData = [];
let categoriesData = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadShopInfo();
    loadCategories();
    loadProducts();
    
    // 绑定表单提交事件
    document.getElementById('shop-form').addEventListener('submit', saveShopInfo);
    document.getElementById('product-form').addEventListener('submit', addProduct);
    
    // 绑定头像上传事件
    document.getElementById('avatar-file').addEventListener('change', uploadAvatar);
});

// 切换标签页
function showTab(tabName) {
    // 隐藏所有内容
    document.querySelectorAll('.content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // 移除所有标签的active类
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 显示选中的内容
    document.getElementById(tabName + '-content').classList.remove('hidden');
    
    // 添加active类到选中的标签
    event.target.classList.add('active');
    
    currentTab = tabName;
    
    // 根据标签页加载相应数据
    if (tabName === 'products') {
        loadProducts();
    }
}

// 显示消息
function showMessage(message, type = 'success', containerId = 'message') {
    const messageDiv = document.getElementById(containerId);
    messageDiv.innerHTML = `<div class="message ${type}">${message}</div>`;
    
    // 3秒后自动隐藏消息
    setTimeout(() => {
        messageDiv.innerHTML = '';
    }, 3000);
}

// 加载店铺信息
async function loadShopInfo() {
    try {
        const response = await fetch('/api/shop');
        const data = await response.json();
        
        shopData = data;
        
        // 填充表单
        document.getElementById('shop_name').value = data.shop_name || '';
        document.getElementById('shop_score').value = data.shop_score || 70;
        document.getElementById('description').value = data.description || '';
        
        // 更新头像预览
        updateAvatarPreview(data.shop_avatar);
        
    } catch (error) {
        console.error('加载店铺信息失败:', error);
        showMessage('加载店铺信息失败', 'error');
    }
}

// 更新头像预览
function updateAvatarPreview(avatar) {
    const preview = document.getElementById('avatar-preview');
    
    if (avatar && avatar !== 'W' && avatar.startsWith('/uploads/')) {
        preview.innerHTML = `<img src="${avatar}" alt="头像">`;
    } else {
        preview.innerHTML = avatar || 'W';
    }
}

// 保存店铺信息
async function saveShopInfo(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await fetch('/api/shop', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            shopData = result;
            showMessage('店铺信息保存成功！');
        } else {
            throw new Error('保存失败');
        }
    } catch (error) {
        console.error('保存店铺信息失败:', error);
        showMessage('保存店铺信息失败', 'error');
    }
}

// 上传头像
async function uploadAvatar(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        showMessage('请选择图片文件', 'error');
        return;
    }
    
    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
        showMessage('图片大小不能超过2MB', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('avatar', file);
    
    try {
        const response = await fetch('/api/shop/avatar', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            updateAvatarPreview(result.avatar_url);
            showMessage('头像上传成功！');
        } else {
            throw new Error('上传失败');
        }
    } catch (error) {
        console.error('上传头像失败:', error);
        showMessage('上传头像失败', 'error');
    }
}

// 加载分类数据
async function loadCategories() {
    try {
        const response = await fetch('/api/categories');
        const data = await response.json();
        
        categoriesData = data;
        
        // 更新分类选择框
        const categorySelect = document.getElementById('product_category');
        categorySelect.innerHTML = '<option value="">请选择分类</option>';
        
        data.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });
        
    } catch (error) {
        console.error('加载分类失败:', error);
    }
}

// 加载商品数据
async function loadProducts() {
    try {
        const response = await fetch('/api/products');
        const data = await response.json();
        
        productsData = data;
        renderProductsTable();
        
    } catch (error) {
        console.error('加载商品失败:', error);
        showMessage('加载商品失败', 'error', 'products-message');
    }
}

// 渲染商品表格
function renderProductsTable() {
    const tbody = document.getElementById('products-table');
    tbody.innerHTML = '';
    
    productsData.forEach(product => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${product.id}</td>
            <td>${product.name}</td>
            <td>${product.category}</td>
            <td>¥${product.price}</td>
            <td>${product.stock}</td>
            <td>${product.sales}</td>
            <td><span class="status-${product.status}">${product.status === 'active' ? '正常' : '停用'}</span></td>
            <td>
                <button class="btn" onclick="editProduct(${product.id})" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                <button class="btn btn-danger" onclick="deleteProduct(${product.id})" style="padding: 6px 12px; font-size: 12px; margin-left: 5px;">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 显示添加商品表单
function showAddProductForm() {
    document.getElementById('add-product-form').classList.remove('hidden');
}

// 隐藏添加商品表单
function hideAddProductForm() {
    document.getElementById('add-product-form').classList.add('hidden');
    document.getElementById('product-form').reset();
}

// 添加商品
async function addProduct(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await fetch('/api/products', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            showMessage('商品添加成功！', 'success', 'products-message');
            hideAddProductForm();
            loadProducts(); // 重新加载商品列表
        } else {
            throw new Error('添加失败');
        }
    } catch (error) {
        console.error('添加商品失败:', error);
        showMessage('添加商品失败', 'error', 'products-message');
    }
}

// 编辑商品
function editProduct(productId) {
    // 这里可以实现编辑商品的功能
    alert('编辑功能开发中...');
}

// 删除商品
async function deleteProduct(productId) {
    if (!confirm('确定要删除这个商品吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/products/${productId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showMessage('商品删除成功！', 'success', 'products-message');
            loadProducts(); // 重新加载商品列表
        } else {
            throw new Error('删除失败');
        }
    } catch (error) {
        console.error('删除商品失败:', error);
        showMessage('删除商品失败', 'error', 'products-message');
    }
}
