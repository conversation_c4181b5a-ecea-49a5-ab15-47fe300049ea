{"name": "douyin-dashboard", "version": "1.0.0", "description": "抖音电商数据仪表板", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:sync": "node scripts/sync-db.js", "db:init": "node scripts/init-database.js", "db:reset": "node scripts/init-database.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "multer": "^2.0.2", "mysql2": "^3.14.3", "node-fetch": "^2.7.0", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["do<PERSON><PERSON>", "dashboard", "ecommerce", "data"], "author": "", "license": "MIT"}