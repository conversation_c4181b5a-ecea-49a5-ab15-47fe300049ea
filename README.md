# 抖音电商数据仪表板

一个基于Node.js + MySQL的抖音电商数据管理系统，支持动态数据管理和后台配置。

## 功能特性

- 📊 **数据仪表板**: 实时显示销售、流量、商品等关键数据
- 🛠️ **后台管理**: 可视化管理店铺信息、商品、数据
- 🏪 **店铺管理**: 动态修改店铺名称、头像、描述等信息
- 📦 **商品管理**: 完整的商品CRUD操作
- 📈 **数据分析**: 销售趋势、市场分析、竞品对比
- 🎨 **响应式设计**: 支持移动端和桌面端访问

## 技术栈

- **后端**: Node.js + Express.js
- **数据库**: MySQL + Sequelize ORM
- **前端**: HTML5 + CSS3 + JavaScript
- **文件上传**: Multer
- **环境配置**: dotenv

## 快速开始

### 1. 环境要求

- Node.js >= 14.0
- MySQL >= 5.7
- npm 或 yarn

### 2. 安装依赖

```bash
npm install
```

### 3. 配置数据库

1. 创建MySQL数据库：
```sql
CREATE DATABASE douyin_dashboard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 复制并配置环境变量：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接信息：
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=douyin_dashboard
DB_USER=root
DB_PASSWORD=your_password
PORT=8083
```

### 4. 初始化数据库

```bash
# 初始化数据库结构和数据
npm run db:init
```

### 5. 启动服务

```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

### 6. 访问应用

- **抖音界面**: http://localhost:8083/douyin-real.html
- **数据罗盘**: http://localhost:8083/data-compass.html
- **后台管理**: http://localhost:8083/admin.html

## 项目结构

```
├── config/
│   └── database.js          # 数据库配置
├── models/
│   └── index.js             # 数据模型定义
├── scripts/
│   ├── init-database.js     # 数据库初始化脚本
│   └── sync-db.js          # 数据库同步脚本
├── uploads/                 # 文件上传目录
├── database/
│   ├── init.sql            # SQL初始化脚本
│   └── seed.sql            # 种子数据
├── server.js               # 主服务器文件
├── admin.html              # 后台管理页面
├── admin.js                # 后台管理脚本
├── douyin-real.html        # 抖音界面
├── data-compass.html       # 数据罗盘
├── package.json
├── .env                    # 环境配置
└── README.md
```

## API接口

### 店铺管理
- `GET /api/shop` - 获取店铺信息
- `PUT /api/shop` - 更新店铺信息
- `POST /api/shop/avatar` - 上传店铺头像

### 商品管理
- `GET /api/products` - 获取商品列表
- `GET /api/products/:id` - 获取商品详情
- `POST /api/products` - 添加商品
- `PUT /api/products/:id` - 更新商品
- `DELETE /api/products/:id` - 删除商品

### 数据接口
- `GET /api/overview` - 获取概览数据
- `GET /api/search` - 获取搜索数据
- `GET /api/trade` - 获取交易数据
- `GET /api/market` - 获取市场数据
- `GET /api/services` - 获取服务数据

### 分类管理
- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 添加分类

## 数据库表结构

- `shop_info` - 店铺信息
- `categories` - 商品分类
- `products` - 商品信息
- `sales_data` - 销售数据
- `traffic_data` - 流量数据
- `channel_distribution` - 渠道分布
- `search_keywords` - 搜索关键词
- `hot_product_searches` - 热门商品搜索
- `payment_methods` - 支付方式统计
- `market_trends` - 市场趋势
- `competitors` - 竞品分析
- `customer_service` - 客服数据
- `logistics` - 物流数据
- `after_sales` - 售后数据

## 使用说明

### 后台管理

1. 访问 http://localhost:8083/admin.html
2. 在"店铺管理"标签页可以：
   - 修改店铺名称
   - 上传店铺头像
   - 设置商家体验分
   - 编辑店铺描述

3. 在"商品管理"标签页可以：
   - 查看所有商品
   - 添加新商品
   - 编辑商品信息
   - 删除商品

### 数据更新

修改店铺信息后，前端页面会自动显示最新的数据，包括：
- 店铺名称
- 店铺头像
- 商家体验分

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 同步数据库结构
npm run db:sync

# 重置数据库（清空并重新初始化）
npm run db:reset
```

## 注意事项

1. 首次运行前请确保MySQL服务已启动
2. 确保数据库用户有足够的权限创建数据库和表
3. 上传的头像文件会保存在 `uploads/` 目录中
4. 建议定期备份数据库数据

## 许可证

MIT License
