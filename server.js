const express = require('express');
const path = require('path');
const cors = require('cors');
const multer = require('multer');
require('dotenv').config();

// 导入数据库配置和模型
const { testConnection } = require('./config/database');
const {
  ShopInfo,
  Category,
  Product,
  SalesData,
  TrafficData,
  ChannelDistribution,
  SearchKeyword,
  HotProductSearch,
  PaymentMethod,
  MarketTrend,
  Competitor,
  CustomerService,
  Logistics,
  AfterSales
} = require('./models');

const app = express();
const PORT = process.env.PORT || 8083;

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/')
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});
const upload = multer({ storage: storage });

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('.'));
app.use('/uploads', express.static('uploads'));

// 测试数据库连接
testConnection();

// 辅助函数：获取昨天的日期
function getYesterday() {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return yesterday.toISOString().split('T')[0];
}

// 辅助函数：获取今天的日期
function getToday() {
  return new Date().toISOString().split('T')[0];
}

// API路由

// 获取概览数据
app.get('/api/overview', async (req, res) => {
  try {
    const today = getToday();
    const yesterday = getYesterday();

    // 获取销售数据
    const todaySales = await SalesData.findOne({ where: { date: today } });
    const yesterdaySales = await SalesData.findOne({ where: { date: yesterday } });

    // 获取流量数据
    const todayTraffic = await TrafficData.findOne({ where: { date: today } });
    const yesterdayTraffic = await TrafficData.findOne({ where: { date: yesterday } });

    // 获取渠道分布数据
    const channelData = await ChannelDistribution.findAll({ where: { date: today } });

    // 计算总销售额
    const totalSales = await SalesData.sum('total_sales');
    const totalOrders = await SalesData.sum('total_orders');

    const overview = {
      paymentAmount: {
        total: totalSales || 0,
        yesterday: yesterdaySales?.total_sales || 0,
        firstPurchase: yesterdaySales?.first_purchase_amount || 0,
        repurchase: yesterdaySales?.repurchase_amount || 0
      },
      orders: {
        total: totalOrders || 0,
        yesterday: yesterdaySales?.total_orders || 0
      },
      avgOrderValue: {
        current: todaySales?.avg_order_value || 0,
        yesterday: yesterdaySales?.avg_order_value || 0
      },
      refundAmount: {
        current: todaySales?.refund_amount || 0,
        yesterday: yesterdaySales?.refund_amount || 0
      },
      exposure: {
        current: todayTraffic?.exposure || 0,
        yesterday: yesterdayTraffic?.exposure || 0
      },
      clicks: {
        current: todayTraffic?.clicks || 0,
        yesterday: yesterdayTraffic?.clicks || 0
      },
      customers: {
        current: todayTraffic?.customers || 0,
        yesterday: yesterdayTraffic?.customers || 0
      },
      channelDistribution: channelData.reduce((acc, item) => {
        const key = item.channel_name.replace(/[^a-zA-Z]/g, '').toLowerCase();
        acc[key] = item.amount;
        return acc;
      }, {})
    };

    res.json(overview);
  } catch (error) {
    console.error('获取概览数据失败:', error);
    res.status(500).json({ error: '获取概览数据失败' });
  }
});

// 获取搜索数据
app.get('/api/search', async (req, res) => {
  try {
    const keywords = await SearchKeyword.findAll({
      order: [['search_volume', 'DESC']],
      limit: 10
    });

    const hotProducts = await HotProductSearch.findAll({
      order: [['searches', 'DESC']],
      limit: 10
    });

    const search = {
      keywords: keywords.map(item => ({
        keyword: item.keyword,
        searchVolume: item.search_volume,
        competition: item.competition,
        trend: item.trend
      })),
      hotProducts: hotProducts.map(item => ({
        name: item.product_name,
        searches: item.searches,
        clicks: item.clicks,
        conversion: `${item.conversion_rate}%`
      }))
    };

    res.json(search);
  } catch (error) {
    console.error('获取搜索数据失败:', error);
    res.status(500).json({ error: '获取搜索数据失败' });
  }
});

// 获取交易数据
app.get('/api/trade', async (req, res) => {
  try {
    // 获取最近7天的销售数据
    const salesData = await SalesData.findAll({
      order: [['date', 'DESC']],
      limit: 7
    });

    // 获取热销商品
    const topProducts = await Product.findAll({
      order: [['sales', 'DESC']],
      limit: 10,
      include: [{
        model: Category,
        as: 'category'
      }]
    });

    // 获取支付方式数据
    const paymentData = await PaymentMethod.findAll({
      where: { date: getToday() }
    });

    const trade = {
      dailyStats: salesData.reverse().map(item => ({
        date: item.date,
        sales: parseFloat(item.total_sales),
        orders: item.total_orders,
        avgOrder: parseFloat(item.avg_order_value)
      })),
      topProducts: topProducts.map(item => ({
        name: item.name,
        sales: item.sales,
        orders: Math.floor(item.sales / 2), // 估算订单数
        revenue: parseFloat(item.price) * item.sales
      })),
      paymentMethods: paymentData.reduce((acc, item) => {
        const key = item.method_name.toLowerCase().replace(/[^a-z]/g, '');
        acc[key] = parseFloat(item.percentage);
        return acc;
      }, {})
    };

    res.json(trade);
  } catch (error) {
    console.error('获取交易数据失败:', error);
    res.status(500).json({ error: '获取交易数据失败' });
  }
});

// 获取商品数据
app.get('/api/products', async (req, res) => {
  try {
    const products = await Product.findAll({
      include: [{
        model: Category,
        as: 'category'
      }],
      where: { status: 'active' }
    });

    const formattedProducts = products.map(item => ({
      id: item.id,
      name: item.name,
      category: item.category?.name || '未分类',
      price: parseFloat(item.price),
      stock: item.stock,
      sales: item.sales,
      rating: parseFloat(item.rating),
      views: item.views,
      conversion: `${item.conversion_rate}%`,
      status: item.status
    }));

    res.json(formattedProducts);
  } catch (error) {
    console.error('获取商品数据失败:', error);
    res.status(500).json({ error: '获取商品数据失败' });
  }
});

// 获取市场数据
app.get('/api/market', async (req, res) => {
  try {
    const trends = await MarketTrend.findAll();
    const competitors = await Competitor.findAll();

    const market = {
      trends: trends.map(item => ({
        category: item.category,
        growth: item.growth_rate,
        volume: item.volume,
        competition: item.competition
      })),
      competitors: competitors.map(item => ({
        name: item.name,
        marketShare: `${item.market_share}%`,
        growth: item.growth_rate,
        strength: item.strength
      }))
    };

    res.json(market);
  } catch (error) {
    console.error('获取市场数据失败:', error);
    res.status(500).json({ error: '获取市场数据失败' });
  }
});

// 获取服务数据
app.get('/api/services', async (req, res) => {
  try {
    const today = getToday();

    const customerService = await CustomerService.findOne({ where: { date: today } });
    const logistics = await Logistics.findOne({ where: { date: today } });
    const afterSales = await AfterSales.findOne({ where: { date: today } });

    const services = {
      customerService: {
        totalTickets: customerService?.total_tickets || 0,
        resolved: customerService?.resolved || 0,
        pending: customerService?.pending || 0,
        avgResponseTime: customerService?.avg_response_time || '0小时',
        satisfaction: parseFloat(customerService?.satisfaction || 0)
      },
      logistics: {
        totalOrders: logistics?.total_orders || 0,
        shipped: logistics?.shipped || 0,
        delivered: logistics?.delivered || 0,
        avgDeliveryTime: logistics?.avg_delivery_time || '0天',
        returnRate: `${logistics?.return_rate || 0}%`
      },
      afterSales: {
        returns: afterSales?.returns || 0,
        exchanges: afterSales?.exchanges || 0,
        refunds: afterSales?.refunds || 0,
        avgProcessTime: afterSales?.avg_process_time || '0天',
        satisfaction: parseFloat(afterSales?.satisfaction || 0)
      }
    };

    res.json(services);
  } catch (error) {
    console.error('获取服务数据失败:', error);
    res.status(500).json({ error: '获取服务数据失败' });
  }
});

// 获取单个商品详情
app.get('/api/products/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    const product = await Product.findByPk(productId, {
      include: [{
        model: Category,
        as: 'category'
      }]
    });

    if (product) {
      const formattedProduct = {
        id: product.id,
        name: product.name,
        category: product.category?.name || '未分类',
        price: parseFloat(product.price),
        stock: product.stock,
        sales: product.sales,
        rating: parseFloat(product.rating),
        views: product.views,
        conversion: `${product.conversion_rate}%`,
        status: product.status,
        description: product.description,
        image_url: product.image_url
      };
      res.json(formattedProduct);
    } else {
      res.status(404).json({ error: '商品未找到' });
    }
  } catch (error) {
    console.error('获取商品详情失败:', error);
    res.status(500).json({ error: '获取商品详情失败' });
  }
});

// 更新商品信息
app.put('/api/products/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    const [updatedRowsCount] = await Product.update(req.body, {
      where: { id: productId }
    });

    if (updatedRowsCount > 0) {
      const updatedProduct = await Product.findByPk(productId, {
        include: [{
          model: Category,
          as: 'category'
        }]
      });
      res.json(updatedProduct);
    } else {
      res.status(404).json({ error: '商品未找到' });
    }
  } catch (error) {
    console.error('更新商品失败:', error);
    res.status(500).json({ error: '更新商品失败' });
  }
});

// 添加新商品
app.post('/api/products', async (req, res) => {
  try {
    const newProduct = await Product.create({
      ...req.body,
      sales: 0,
      views: 0,
      conversion_rate: 0,
      status: 'active'
    });

    const productWithCategory = await Product.findByPk(newProduct.id, {
      include: [{
        model: Category,
        as: 'category'
      }]
    });

    res.status(201).json(productWithCategory);
  } catch (error) {
    console.error('添加商品失败:', error);
    res.status(500).json({ error: '添加商品失败' });
  }
});

// 删除商品
app.delete('/api/products/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    const deletedRowsCount = await Product.destroy({
      where: { id: productId }
    });

    if (deletedRowsCount > 0) {
      res.json({ message: '商品删除成功' });
    } else {
      res.status(404).json({ error: '商品未找到' });
    }
  } catch (error) {
    console.error('删除商品失败:', error);
    res.status(500).json({ error: '删除商品失败' });
  }
});

// 店铺信息管理API

// 获取店铺信息
app.get('/api/shop', async (req, res) => {
  try {
    const shop = await ShopInfo.findOne();
    if (shop) {
      res.json(shop);
    } else {
      // 如果没有店铺信息，返回默认值
      res.json({
        shop_name: '文恩臻选阁',
        shop_avatar: 'W',
        shop_score: 70,
        description: '精选优质商品，为您提供最佳购物体验',
        background_image: './2.png'
      });
    }
  } catch (error) {
    console.error('获取店铺信息失败:', error);
    res.status(500).json({ error: '获取店铺信息失败' });
  }
});

// 更新店铺信息
app.put('/api/shop', async (req, res) => {
  try {
    let shop = await ShopInfo.findOne();

    if (shop) {
      await shop.update(req.body);
    } else {
      shop = await ShopInfo.create(req.body);
    }

    res.json(shop);
  } catch (error) {
    console.error('更新店铺信息失败:', error);
    res.status(500).json({ error: '更新店铺信息失败' });
  }
});

// 上传头像
app.post('/api/shop/avatar', upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '请选择头像文件' });
    }

    const avatarUrl = `/uploads/${req.file.filename}`;

    let shop = await ShopInfo.findOne();
    if (shop) {
      await shop.update({ shop_avatar: avatarUrl });
    } else {
      await ShopInfo.create({
        shop_name: '文恩臻选阁',
        shop_avatar: avatarUrl,
        shop_score: 70
      });
    }

    res.json({ avatar_url: avatarUrl });
  } catch (error) {
    console.error('上传头像失败:', error);
    res.status(500).json({ error: '上传头像失败' });
  }
});

// 分类管理API
app.get('/api/categories', async (req, res) => {
  try {
    const categories = await Category.findAll();
    res.json(categories);
  } catch (error) {
    console.error('获取分类失败:', error);
    res.status(500).json({ error: '获取分类失败' });
  }
});

app.post('/api/categories', async (req, res) => {
  try {
    const category = await Category.create(req.body);
    res.status(201).json(category);
  } catch (error) {
    console.error('创建分类失败:', error);
    res.status(500).json({ error: '创建分类失败' });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📊 抖音界面: http://localhost:${PORT}/douyin-real.html`);
  console.log(`📊 数据罗盘: http://localhost:${PORT}/data-compass.html`);
  console.log(`🔧 后台管理: http://localhost:${PORT}/admin.html`);
  console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n正在关闭服务器...');
  process.exit(0);
});
