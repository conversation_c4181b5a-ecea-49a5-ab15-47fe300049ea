const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8083;

// 中间件
app.use(express.json());
app.use(express.static('.'));

// 模拟数据库
const mockData = {
  // 首页数据
  overview: {
    paymentAmount: {
      total: 128456,
      yesterday: 18234,
      firstPurchase: 76234,
      repurchase: 52222
    },
    orders: {
      total: 1247,
      yesterday: 189
    },
    avgOrderValue: {
      current: 103,
      yesterday: 96
    },
    refundAmount: {
      current: 3245,
      yesterday: 567
    },
    exposure: {
      current: 15678,
      yesterday: 12456
    },
    clicks: {
      current: 8234,
      yesterday: 6789
    },
    customers: {
      current: 1156,
      yesterday: 987
    },
    channelDistribution: {
      productCard: 45678,
      shortVideo: 67234,
      liveStream: 15544,
      article: 8234,
      other: 2156
    }
  },

  // 搜索数据
  search: {
    keywords: [
      { keyword: '美妆护肤', searchVolume: 125000, competition: 'high', trend: '+15%' },
      { keyword: '服装搭配', searchVolume: 98000, competition: 'medium', trend: '+8%' },
      { keyword: '数码产品', searchVolume: 87000, competition: 'high', trend: '+22%' },
      { keyword: '家居用品', searchVolume: 76000, competition: 'low', trend: '+5%' },
      { keyword: '食品饮料', searchVolume: 65000, competition: 'medium', trend: '+12%' }
    ],
    hotProducts: [
      { name: '口红套装', searches: 45000, clicks: 12000, conversion: '8.5%' },
      { name: '连衣裙', searches: 38000, clicks: 9500, conversion: '6.2%' },
      { name: '手机壳', searches: 32000, clicks: 8900, conversion: '12.1%' },
      { name: '咖啡机', searches: 28000, clicks: 7200, conversion: '9.8%' }
    ]
  },

  // 交易数据
  trade: {
    dailyStats: [
      { date: '2024-01-01', sales: 15600, orders: 156, avgOrder: 100 },
      { date: '2024-01-02', sales: 18200, orders: 182, avgOrder: 100 },
      { date: '2024-01-03', sales: 22400, orders: 224, avgOrder: 100 },
      { date: '2024-01-04', sales: 19800, orders: 198, avgOrder: 100 },
      { date: '2024-01-05', sales: 25600, orders: 256, avgOrder: 100 },
      { date: '2024-01-06', sales: 21200, orders: 212, avgOrder: 100 },
      { date: '2024-01-07', sales: 18234, orders: 189, avgOrder: 96 }
    ],
    topProducts: [
      { name: '精华液套装', sales: 25600, orders: 128, revenue: 3276800 },
      { name: '无线耳机', sales: 18900, orders: 189, revenue: 1890000 },
      { name: '运动鞋', sales: 15600, orders: 78, revenue: 2340000 },
      { name: '保温杯', sales: 12300, orders: 246, revenue: 615000 }
    ],
    paymentMethods: {
      alipay: 45.2,
      wechat: 38.7,
      credit: 12.1,
      other: 4.0
    }
  },

  // 商品数据
  products: [
    {
      id: 1,
      name: '美白精华液',
      category: '美妆护肤',
      price: 299,
      stock: 1250,
      sales: 856,
      rating: 4.8,
      views: 15600,
      conversion: '5.5%',
      status: 'active'
    },
    {
      id: 2,
      name: '蓝牙耳机',
      category: '数码产品',
      price: 199,
      stock: 890,
      sales: 634,
      rating: 4.6,
      views: 12400,
      conversion: '5.1%',
      status: 'active'
    },
    {
      id: 3,
      name: '连衣裙',
      category: '服装',
      price: 159,
      stock: 456,
      sales: 423,
      rating: 4.7,
      views: 9800,
      conversion: '4.3%',
      status: 'active'
    },
    {
      id: 4,
      name: '咖啡机',
      category: '家电',
      price: 899,
      stock: 234,
      sales: 167,
      rating: 4.9,
      views: 5600,
      conversion: '3.0%',
      status: 'active'
    }
  ],

  // 市场数据
  market: {
    trends: [
      { category: '美妆护肤', growth: '+25%', volume: 2500000, competition: 'high' },
      { category: '服装配饰', growth: '+18%', volume: 1800000, competition: 'high' },
      { category: '数码产品', growth: '+32%', volume: 1200000, competition: 'medium' },
      { category: '家居用品', growth: '+15%', volume: 980000, competition: 'low' },
      { category: '食品饮料', growth: '+22%', volume: 750000, competition: 'medium' }
    ],
    competitors: [
      { name: '竞品A', marketShare: '15.2%', growth: '+8%', strength: '品牌知名度' },
      { name: '竞品B', marketShare: '12.8%', growth: '+12%', strength: '价格优势' },
      { name: '竞品C', marketShare: '9.5%', growth: '+5%', strength: '产品质量' },
      { name: '竞品D', marketShare: '7.3%', growth: '+18%', strength: '营销创新' }
    ]
  },

  // 服务数据
  services: {
    customerService: {
      totalTickets: 1250,
      resolved: 1180,
      pending: 70,
      avgResponseTime: '2.5小时',
      satisfaction: 4.6
    },
    logistics: {
      totalOrders: 2340,
      shipped: 2180,
      delivered: 2050,
      avgDeliveryTime: '1.8天',
      returnRate: '2.1%'
    },
    afterSales: {
      returns: 156,
      exchanges: 89,
      refunds: 67,
      avgProcessTime: '1.2天',
      satisfaction: 4.4
    }
  }
};

// API路由
app.get('/api/overview', (req, res) => {
  res.json(mockData.overview);
});

app.get('/api/search', (req, res) => {
  res.json(mockData.search);
});

app.get('/api/trade', (req, res) => {
  res.json(mockData.trade);
});

app.get('/api/products', (req, res) => {
  res.json(mockData.products);
});

app.get('/api/market', (req, res) => {
  res.json(mockData.market);
});

app.get('/api/services', (req, res) => {
  res.json(mockData.services);
});

// 获取单个商品详情
app.get('/api/products/:id', (req, res) => {
  const productId = parseInt(req.params.id);
  const product = mockData.products.find(p => p.id === productId);
  if (product) {
    res.json(product);
  } else {
    res.status(404).json({ error: '商品未找到' });
  }
});

// 更新商品信息
app.put('/api/products/:id', (req, res) => {
  const productId = parseInt(req.params.id);
  const productIndex = mockData.products.findIndex(p => p.id === productId);
  
  if (productIndex !== -1) {
    mockData.products[productIndex] = { ...mockData.products[productIndex], ...req.body };
    res.json(mockData.products[productIndex]);
  } else {
    res.status(404).json({ error: '商品未找到' });
  }
});

// 添加新商品
app.post('/api/products', (req, res) => {
  const newProduct = {
    id: mockData.products.length + 1,
    ...req.body,
    sales: 0,
    views: 0,
    conversion: '0%',
    status: 'active'
  };
  mockData.products.push(newProduct);
  res.status(201).json(newProduct);
});

// 删除商品
app.delete('/api/products/:id', (req, res) => {
  const productId = parseInt(req.params.id);
  const productIndex = mockData.products.findIndex(p => p.id === productId);
  
  if (productIndex !== -1) {
    mockData.products.splice(productIndex, 1);
    res.json({ message: '商品删除成功' });
  } else {
    res.status(404).json({ error: '商品未找到' });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📊 抖音界面: http://localhost:${PORT}/douyin-real.html`);
  console.log(`📊 数据罗盘: http://localhost:${PORT}/data-compass.html`);
  console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n正在关闭服务器...');
  process.exit(0);
});
