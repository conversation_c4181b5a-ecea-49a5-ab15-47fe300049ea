const { Sequelize } = require('sequelize');
require('dotenv').config();

async function initDatabase() {
  try {
    console.log('🔄 开始初始化数据库...');

    // 首先连接到MySQL服务器（不指定数据库）
    const sequelizeRoot = new Sequelize('', process.env.DB_USER, process.env.DB_PASSWORD, {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'mysql',
      logging: false
    });

    // 创建数据库
    await sequelizeRoot.query(`CREATE DATABASE IF NOT EXISTS \`${process.env.DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`);
    console.log('✅ 数据库创建完成');

    await sequelizeRoot.close();

    // 现在导入模型并连接到新创建的数据库
    const {
      sequelize,
      ShopInfo,
      Category,
      Product,
      SalesData,
      TrafficData,
      ChannelDistribution,
      SearchKeyword,
      HotProductSearch,
      PaymentMethod,
      MarketTrend,
      Competitor,
      CustomerService,
      Logistics,
      AfterSales
    } = require('../models');

    // 同步数据库结构
    await sequelize.sync({ force: true });
    console.log('✅ 数据库结构同步完成');
    
    // 插入店铺信息
    await ShopInfo.create({
      shop_name: '文恩臻选阁',
      shop_avatar: 'W',
      shop_score: 70,
      description: '精选优质商品，为您提供最佳购物体验',
      background_image: './2.png'
    });
    console.log('✅ 店铺信息初始化完成');
    
    // 插入商品分类
    const categories = await Category.bulkCreate([
      { name: '美妆护肤', description: '美容护肤产品' },
      { name: '数码产品', description: '电子数码设备' },
      { name: '服装', description: '时尚服装配饰' },
      { name: '家电', description: '家用电器' },
      { name: '食品饮料', description: '食品和饮料' }
    ]);
    console.log('✅ 商品分类初始化完成');
    
    // 插入商品数据
    await Product.bulkCreate([
      {
        name: '美白精华液',
        category_id: categories[0].id,
        price: 299.00,
        stock: 1250,
        sales: 856,
        rating: 4.8,
        views: 15600,
        conversion_rate: 5.5,
        status: 'active',
        description: '高效美白精华，温和不刺激'
      },
      {
        name: '蓝牙耳机',
        category_id: categories[1].id,
        price: 199.00,
        stock: 890,
        sales: 634,
        rating: 4.6,
        views: 12400,
        conversion_rate: 5.1,
        status: 'active',
        description: '高品质无线蓝牙耳机'
      },
      {
        name: '连衣裙',
        category_id: categories[2].id,
        price: 159.00,
        stock: 456,
        sales: 423,
        rating: 4.7,
        views: 9800,
        conversion_rate: 4.3,
        status: 'active',
        description: '时尚优雅连衣裙'
      },
      {
        name: '咖啡机',
        category_id: categories[3].id,
        price: 899.00,
        stock: 234,
        sales: 167,
        rating: 4.9,
        views: 5600,
        conversion_rate: 3.0,
        status: 'active',
        description: '全自动咖啡机'
      }
    ]);
    console.log('✅ 商品数据初始化完成');
    
    // 插入销售数据
    await SalesData.bulkCreate([
      { date: '2024-01-01', total_sales: 15600.00, total_orders: 156, avg_order_value: 100.00, first_purchase_amount: 9360.00, repurchase_amount: 6240.00, refund_amount: 780.00 },
      { date: '2024-01-02', total_sales: 18200.00, total_orders: 182, avg_order_value: 100.00, first_purchase_amount: 10920.00, repurchase_amount: 7280.00, refund_amount: 910.00 },
      { date: '2024-01-03', total_sales: 22400.00, total_orders: 224, avg_order_value: 100.00, first_purchase_amount: 13440.00, repurchase_amount: 8960.00, refund_amount: 1120.00 },
      { date: '2024-01-04', total_sales: 19800.00, total_orders: 198, avg_order_value: 100.00, first_purchase_amount: 11880.00, repurchase_amount: 7920.00, refund_amount: 990.00 },
      { date: '2024-01-05', total_sales: 25600.00, total_orders: 256, avg_order_value: 100.00, first_purchase_amount: 15360.00, repurchase_amount: 10240.00, refund_amount: 1280.00 },
      { date: '2024-01-06', total_sales: 21200.00, total_orders: 212, avg_order_value: 100.00, first_purchase_amount: 12720.00, repurchase_amount: 8480.00, refund_amount: 1060.00 },
      { date: '2024-01-07', total_sales: 18234.00, total_orders: 189, avg_order_value: 96.00, first_purchase_amount: 10940.40, repurchase_amount: 7293.60, refund_amount: 911.70 }
    ]);
    console.log('✅ 销售数据初始化完成');
    
    // 插入流量数据
    await TrafficData.bulkCreate([
      { date: '2024-01-01', exposure: 12000, clicks: 6000, customers: 800 },
      { date: '2024-01-02', exposure: 13500, clicks: 6750, customers: 900 },
      { date: '2024-01-03', exposure: 16800, clicks: 8400, customers: 1120 },
      { date: '2024-01-04', exposure: 14850, clicks: 7425, customers: 990 },
      { date: '2024-01-05', exposure: 19200, clicks: 9600, customers: 1280 },
      { date: '2024-01-06', exposure: 15900, clicks: 7950, customers: 1060 },
      { date: '2024-01-07', exposure: 15678, clicks: 8234, customers: 1156 }
    ]);
    console.log('✅ 流量数据初始化完成');
    
    // 插入渠道分布数据
    await ChannelDistribution.bulkCreate([
      { channel_name: '商品卡片', amount: 45678.00, date: '2024-01-07' },
      { channel_name: '短视频', amount: 67234.00, date: '2024-01-07' },
      { channel_name: '直播', amount: 15544.00, date: '2024-01-07' },
      { channel_name: '图文', amount: 8234.00, date: '2024-01-07' },
      { channel_name: '其他', amount: 2156.00, date: '2024-01-07' }
    ]);
    console.log('✅ 渠道分布数据初始化完成');
    
    // 插入搜索关键词
    await SearchKeyword.bulkCreate([
      { keyword: '美妆护肤', search_volume: 125000, competition: 'high', trend: '+15%' },
      { keyword: '服装搭配', search_volume: 98000, competition: 'medium', trend: '+8%' },
      { keyword: '数码产品', search_volume: 87000, competition: 'high', trend: '+22%' },
      { keyword: '家居用品', search_volume: 76000, competition: 'low', trend: '+5%' },
      { keyword: '食品饮料', search_volume: 65000, competition: 'medium', trend: '+12%' }
    ]);
    console.log('✅ 搜索关键词初始化完成');
    
    // 插入热门商品搜索
    await HotProductSearch.bulkCreate([
      { product_name: '口红套装', searches: 45000, clicks: 12000, conversion_rate: 8.5 },
      { product_name: '连衣裙', searches: 38000, clicks: 9500, conversion_rate: 6.2 },
      { product_name: '手机壳', searches: 32000, clicks: 8900, conversion_rate: 12.1 },
      { product_name: '咖啡机', searches: 28000, clicks: 7200, conversion_rate: 9.8 }
    ]);
    console.log('✅ 热门商品搜索初始化完成');
    
    // 插入支付方式统计
    await PaymentMethod.bulkCreate([
      { method_name: '支付宝', percentage: 45.2, date: '2024-01-07' },
      { method_name: '微信支付', percentage: 38.7, date: '2024-01-07' },
      { method_name: '信用卡', percentage: 12.1, date: '2024-01-07' },
      { method_name: '其他', percentage: 4.0, date: '2024-01-07' }
    ]);
    console.log('✅ 支付方式统计初始化完成');
    
    // 插入市场趋势
    await MarketTrend.bulkCreate([
      { category: '美妆护肤', growth_rate: '+25%', volume: 2500000, competition: 'high' },
      { category: '服装配饰', growth_rate: '+18%', volume: 1800000, competition: 'high' },
      { category: '数码产品', growth_rate: '+32%', volume: 1200000, competition: 'medium' },
      { category: '家居用品', growth_rate: '+15%', volume: 980000, competition: 'low' },
      { category: '食品饮料', growth_rate: '+22%', volume: 750000, competition: 'medium' }
    ]);
    console.log('✅ 市场趋势初始化完成');
    
    // 插入竞品分析
    await Competitor.bulkCreate([
      { name: '竞品A', market_share: 15.2, growth_rate: '+8%', strength: '品牌知名度' },
      { name: '竞品B', market_share: 12.8, growth_rate: '+12%', strength: '价格优势' },
      { name: '竞品C', market_share: 9.5, growth_rate: '+5%', strength: '产品质量' },
      { name: '竞品D', market_share: 7.3, growth_rate: '+18%', strength: '营销创新' }
    ]);
    console.log('✅ 竞品分析初始化完成');
    
    // 插入客服数据
    await CustomerService.create({
      date: '2024-01-07',
      total_tickets: 1250,
      resolved: 1180,
      pending: 70,
      avg_response_time: '2.5小时',
      satisfaction: 4.6
    });
    console.log('✅ 客服数据初始化完成');
    
    // 插入物流数据
    await Logistics.create({
      date: '2024-01-07',
      total_orders: 2340,
      shipped: 2180,
      delivered: 2050,
      avg_delivery_time: '1.8天',
      return_rate: 2.1
    });
    console.log('✅ 物流数据初始化完成');
    
    // 插入售后数据
    await AfterSales.create({
      date: '2024-01-07',
      returns: 156,
      exchanges: 89,
      refunds: 67,
      avg_process_time: '1.2天',
      satisfaction: 4.4
    });
    console.log('✅ 售后数据初始化完成');
    
    console.log('🎉 数据库初始化完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  }
}

initDatabase();
